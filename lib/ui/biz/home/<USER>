import 'dart:ui' as ui;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '/models/business.dart' show EventItem;
import '/provider/business.dart';
import '/provider/card.dart';
import '/provider/user.dart';

// 自定义的安全区域感知的 SliverPinnedHeader
// 通过监听滚动位置来动态调整是否显示安全区域偏移
class SafeAreaSliverPinnedHeader extends StatefulWidget {
  const SafeAreaSliverPinnedHeader({
    super.key,
    required this.child,
    required this.topPadding,
    this.backgroundColor,
  });

  final Widget child;
  final double topPadding;
  final Color? backgroundColor;

  @override
  State<SafeAreaSliverPinnedHeader> createState() => _SafeAreaSliverPinnedHeaderState();
}

class _SafeAreaSliverPinnedHeaderState extends State<SafeAreaSliverPinnedHeader> {
  bool _isPinned = false;
  final GlobalKey _containerKey = GlobalKey();

  void _checkPinned() {
    if (!mounted) {
      return;
    }

    final RenderBox? renderBox = _containerKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      return;
    }

    try {
      final position = renderBox.localToGlobal(Offset.zero);
      final shouldBePinned = position.dy <= 0;

      if (shouldBePinned != _isPinned) {
        setState(() {
          _isPinned = shouldBePinned;
        });
      }
    } catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    // 在每次 build 时检测位置
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkPinned());

    return SliverPinnedHeader(
      child: Container(
        key: _containerKey,
        color: widget.backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 只有在吸顶时才显示安全区域占位符
            if (_isPinned) SizedBox(height: widget.topPadding),
            widget.child,
          ],
        ),
      ),
    );
  }
}

// 分组数据结构
@immutable
class EventCategory {
  const EventCategory({
    required this.title,
    required this.children,
  });

  final String title;
  final List<EventItem> children;

  @override
  String toString() {
    return 'EventCategory(title: $title, children: $children)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is EventCategory && other.title == title && other.children == children;
  }

  @override
  int get hashCode => Object.hash(title, children);
}

class Fun extends StatelessWidget {
  const Fun({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: const BrightnessLayer(
        brightness: Brightness.dark,
        child: _MainBody(),
      ),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  /// 按category分组events并排序
  List<EventCategory> _groupEventsByCategory(List<EventItem> events) {
    // 按category分组
    final Map<String, List<EventItem>> grouped = {};

    for (final event in events) {
      final category = event.eventType.or('Other');
      grouped.putIfAbsent(category, () => []).add(event);
    }

    // 创建分组对象并对每个分组内的项目排序
    final List<EventCategory> categories = grouped.entries.map<EventCategory>((entry) {
      final List<EventItem> sortedChildren = List<EventItem>.from(entry.value);
      // 按sort字段从小到大排序
      sortedChildren.sort((a, b) => (a.sort ?? 0).compareTo(b.sort ?? 0));

      return EventCategory(title: entry.key, children: sortedChildren);
    }).toList();

    // 可以根据需要对category本身进行排序
    categories.sort((a, b) => a.title.compareTo(b.title));

    return categories;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final events = ref.watch(configProvider).events;
    final cards = ref.watch(fetchMyCardsProvider);
    final cardEventIds = (cards.valueOrNull ?? []).map((c) => c.card3EventId).toSet();
    final filteredEvents = events.where((event) {
      final eventIds = event.eventIds;
      return eventIds.isNotEmpty ? eventIds.any((id) => cardEventIds.contains(id)) : true;
    }).toList();

    // 按category分组并创建二维数组结构
    final groupedEvents = _groupEventsByCategory(filteredEvents);

    // 在 removePadding 之前获取原始的安全区域高度
    final originalTopPadding = MediaQuery.paddingOf(context).top;

    return Scaffold(
      body: MediaQuery.removePadding(
        removeTop: true,
        removeBottom: true,
        context: context,
        child: RefreshIndicator(
          displacement: originalTopPadding,
          onRefresh: () async {
            ref.invalidate(fetchUserInfoProvider);
            ref.invalidate(fetchMyCardsProvider);
            await Future.wait([
              ref.read(fetchUserInfoProvider().future),
              ref.read(fetchMyCardsProvider.future),
              ref.read(configStateProvider.notifier).loadConfig(),
            ]);
          },
          child: CustomScrollView(
            slivers: [
              SliverGap.topPadding(context, 16.0),
              const SliverToBoxAdapter(child: _PointsCard()),
              ...groupedEvents.map((category) {
                return MultiSliver(
                  pushPinnedChildren: true,
                  children: [
                    SafeAreaSliverPinnedHeader(
                      // 传入原始的安全区域高度
                      topPadding: originalTopPadding,
                      // 使用应用的深色背景色，确保与页面背景一致
                      backgroundColor: ColorName.backgroundColorDark, // #0b0b0f
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                        child: Text(
                          category.title,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SliverGap.v(8.0),
                    SliverPadding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      sliver: SliverGrid(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final event = category.children[index];
                            return _buildFeatureCard(
                              context,
                              title: event.title,
                              subtitle: event.desc,
                              onTap: () {
                                if (event.isNative) {
                                  if (event.title == 'Connections') {
                                    meNavigator.pushNamed(Routes.funConnection.name);
                                  } else if (event.title == 'Referral') {
                                    meNavigator.pushNamed(Routes.funReferral.name);
                                  }
                                } else {
                                  meNavigator.pushNamed(
                                    Routes.webview.name,
                                    arguments: Routes.webview.d(
                                      url: '$envUrlCard3${event.link}',
                                      title: event.title,
                                    ),
                                  );
                                }
                              },
                              icon: Container(
                                width: 56,
                                height: 56,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                ),
                                child: event.blendMode
                                    ? BackdropFilter(
                                        filter: ui.ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                                        blendMode: BlendMode.lighten,
                                        child: MEImage(
                                          event.image,
                                          fit: BoxFit.contain,
                                          height: 56,
                                        ),
                                      )
                                    : MEImage(
                                        event.image,
                                        fit: BoxFit.contain,
                                        height: 56,
                                      ),
                              ),
                            );
                          },
                          childCount: category.children.length,
                        ),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                          childAspectRatio: 1.3,
                        ),
                      ),
                    ),
                  ],
                );
              }),
              SliverGap.v(500.0),
            ],
          ),
        ),
      ),
    );
  }

  // 构建功能卡片的辅助方法
  Widget _buildFeatureCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Widget icon,
    VoidCallback? onTap,
  }) {
    return Tapper(
      onTap: onTap,
      child: Container(
        constraints: const BoxConstraints(minHeight: 140),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: context.theme.cardColor,
        ),
        padding: const EdgeInsets.all(12),
        child: Column(
          spacing: 8.0,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              spacing: 4.0,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            // 使用Align替代Row，确保图标总是在右下角
            Expanded(
              child: Align(
                alignment: AlignmentDirectional.bottomEnd,
                child: icon,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PointsCard extends ConsumerWidget {
  const _PointsCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userInfo = ref.watch(fetchUserInfoProvider());
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: context.theme.cardColor,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: GestureDetector(
                  onTap: () {
                    // 导航到积分记录页面
                    meNavigator.pushNamed(Routes.funPointRecord.name);
                  },
                  child: const Icon(Icons.history, size: 30),
                ),
              ),
            ],
          ),
          const Text(
            'Points',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 24,
            ),
          ),
          Text(
            userInfo.valueOrNull?.integral.toString() ?? '---',
            style: const TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              // 导航到积分记录页面
              meNavigator.pushNamed(Routes.funPointRecord.name);
            },
            child: const Text(
              'Point Records',
              style: TextStyle(
                color: Color(0xFFFFD700),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}

// 添加AnimatedAppLogo组件
class _AnimatedAppLogo extends StatefulWidget {
  const _AnimatedAppLogo();

  @override
  State<_AnimatedAppLogo> createState() => _AnimatedAppLogoState();
}

class _AnimatedAppLogoState extends State<_AnimatedAppLogo> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true); // 反向重复播放动画

    // 创建一个从0.5到1.0的透明度动画
    _opacityAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: child,
        );
      },
      child: Assets.icons.headerFun.svg(width: 413.0, fit: BoxFit.cover),
    );
  }
}
